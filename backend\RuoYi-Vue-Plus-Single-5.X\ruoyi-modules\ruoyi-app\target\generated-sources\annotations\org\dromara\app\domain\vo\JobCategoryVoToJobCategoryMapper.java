package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__77;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.JobCategory;
import org.dromara.app.domain.JobCategoryToJobCategoryVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__77.class,
    uses = {JobCategoryToJobCategoryVoMapper.class},
    imports = {}
)
public interface JobCategoryVoToJobCategoryMapper extends BaseMapper<JobCategoryVo, JobCategory> {
}
