package org.dromara.app.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.app.domain.Question;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T12:02:22+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.10 (Oracle Corporation)"
)
@Component
public class QuestionBoToQuestionMapperImpl implements QuestionBoToQuestionMapper {

    @Override
    public Question convert(QuestionBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Question question = new Question();

        question.setSearchValue( arg0.getSearchValue() );
        question.setCreateDept( arg0.getCreateDept() );
        question.setCreateBy( arg0.getCreateBy() );
        question.setCreateTime( arg0.getCreateTime() );
        question.setUpdateBy( arg0.getUpdateBy() );
        question.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            question.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        question.setQuestionId( arg0.getQuestionId() );
        question.setBankId( arg0.getBankId() );
        question.setQuestionCode( arg0.getQuestionCode() );
        question.setTitle( arg0.getTitle() );
        question.setDescription( arg0.getDescription() );
        question.setContent( arg0.getContent() );
        question.setAnswer( arg0.getAnswer() );
        question.setAnalysis( arg0.getAnalysis() );
        question.setDifficulty( arg0.getDifficulty() );
        question.setCategory( arg0.getCategory() );
        question.setType( arg0.getType() );
        question.setPracticeCount( arg0.getPracticeCount() );
        question.setCorrectRate( arg0.getCorrectRate() );
        question.setAcceptanceRate( arg0.getAcceptanceRate() );
        question.setCommentCount( arg0.getCommentCount() );
        question.setTags( arg0.getTags() );
        question.setSort( arg0.getSort() );
        question.setStatus( arg0.getStatus() );
        question.setRemark( arg0.getRemark() );

        return question;
    }

    @Override
    public Question convert(QuestionBo arg0, Question arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setQuestionId( arg0.getQuestionId() );
        arg1.setBankId( arg0.getBankId() );
        arg1.setQuestionCode( arg0.getQuestionCode() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setDescription( arg0.getDescription() );
        arg1.setContent( arg0.getContent() );
        arg1.setAnswer( arg0.getAnswer() );
        arg1.setAnalysis( arg0.getAnalysis() );
        arg1.setDifficulty( arg0.getDifficulty() );
        arg1.setCategory( arg0.getCategory() );
        arg1.setType( arg0.getType() );
        arg1.setPracticeCount( arg0.getPracticeCount() );
        arg1.setCorrectRate( arg0.getCorrectRate() );
        arg1.setAcceptanceRate( arg0.getAcceptanceRate() );
        arg1.setCommentCount( arg0.getCommentCount() );
        arg1.setTags( arg0.getTags() );
        arg1.setSort( arg0.getSort() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setRemark( arg0.getRemark() );

        return arg1;
    }
}
