package org.dromara.app.domain;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import javax.annotation.processing.Generated;
import org.dromara.app.domain.vo.VideoDetailVo;
import org.dromara.app.utils.VideoMappingUtils;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T12:02:22+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.10 (Oracle Corporation)"
)
@Component
public class VideoToVideoDetailVoMapperImpl implements VideoToVideoDetailVoMapper {

    @Override
    public VideoDetailVo convert(Video arg0) {
        if ( arg0 == null ) {
            return null;
        }

        VideoDetailVo videoDetailVo = new VideoDetailVo();

        videoDetailVo.setId( arg0.getId() );
        videoDetailVo.setTitle( arg0.getTitle() );
        videoDetailVo.setDescription( arg0.getDescription() );
        videoDetailVo.setInstructor( arg0.getInstructor() );
        videoDetailVo.setInstructorAvatar( arg0.getInstructorAvatar() );
        videoDetailVo.setInstructorId( arg0.getInstructorId() );
        videoDetailVo.setDuration( arg0.getDuration() );
        videoDetailVo.setThumbnail( arg0.getThumbnail() );
        videoDetailVo.setCategory( arg0.getCategory() );
        videoDetailVo.setDifficulty( arg0.getDifficulty() );
        videoDetailVo.setRating( arg0.getRating() );
        videoDetailVo.setStudentCount( arg0.getStudentCount() );
        videoDetailVo.setPrice( arg0.getPrice() );
        videoDetailVo.setFree( VideoMappingUtils.map( arg0.getFree() ) );
        videoDetailVo.setVideoUrl( arg0.getVideoUrl() );
        videoDetailVo.setViewCount( arg0.getViewCount() );
        videoDetailVo.setLikeCount( arg0.getLikeCount() );
        videoDetailVo.setCollectCount( arg0.getCollectCount() );
        videoDetailVo.setShareCount( arg0.getShareCount() );
        videoDetailVo.setTags( VideoMappingUtils.map( arg0.getTags() ) );
        videoDetailVo.setPublishTime( arg0.getPublishTime() );
        if ( arg0.getCreateTime() != null ) {
            videoDetailVo.setCreateTime( LocalDateTime.ofInstant( arg0.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        if ( arg0.getUpdateTime() != null ) {
            videoDetailVo.setUpdateTime( LocalDateTime.ofInstant( arg0.getUpdateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }

        return videoDetailVo;
    }

    @Override
    public VideoDetailVo convert(Video arg0, VideoDetailVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setDescription( arg0.getDescription() );
        arg1.setInstructor( arg0.getInstructor() );
        arg1.setInstructorAvatar( arg0.getInstructorAvatar() );
        arg1.setInstructorId( arg0.getInstructorId() );
        arg1.setDuration( arg0.getDuration() );
        arg1.setThumbnail( arg0.getThumbnail() );
        arg1.setCategory( arg0.getCategory() );
        arg1.setDifficulty( arg0.getDifficulty() );
        arg1.setRating( arg0.getRating() );
        arg1.setStudentCount( arg0.getStudentCount() );
        arg1.setPrice( arg0.getPrice() );
        arg1.setFree( VideoMappingUtils.map( arg0.getFree() ) );
        arg1.setVideoUrl( arg0.getVideoUrl() );
        arg1.setViewCount( arg0.getViewCount() );
        arg1.setLikeCount( arg0.getLikeCount() );
        arg1.setCollectCount( arg0.getCollectCount() );
        arg1.setShareCount( arg0.getShareCount() );
        if ( arg1.getTags() != null ) {
            List<String> list = VideoMappingUtils.map( arg0.getTags() );
            if ( list != null ) {
                arg1.getTags().clear();
                arg1.getTags().addAll( list );
            }
            else {
                arg1.setTags( null );
            }
        }
        else {
            List<String> list = VideoMappingUtils.map( arg0.getTags() );
            if ( list != null ) {
                arg1.setTags( list );
            }
        }
        arg1.setPublishTime( arg0.getPublishTime() );
        if ( arg0.getCreateTime() != null ) {
            arg1.setCreateTime( LocalDateTime.ofInstant( arg0.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        else {
            arg1.setCreateTime( null );
        }
        if ( arg0.getUpdateTime() != null ) {
            arg1.setUpdateTime( LocalDateTime.ofInstant( arg0.getUpdateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        else {
            arg1.setUpdateTime( null );
        }

        return arg1;
    }
}
