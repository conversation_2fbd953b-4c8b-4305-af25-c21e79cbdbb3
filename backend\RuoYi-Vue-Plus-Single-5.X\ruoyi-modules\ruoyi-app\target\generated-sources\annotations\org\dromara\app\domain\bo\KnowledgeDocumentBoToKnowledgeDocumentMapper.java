package org.dromara.app.domain.bo;

import io.github.linpeilie.AutoMapperConfig__77;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.KnowledgeDocument;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__77.class,
    uses = {},
    imports = {}
)
public interface KnowledgeDocumentBoToKnowledgeDocumentMapper extends BaseMapper<KnowledgeDocumentBo, KnowledgeDocument> {
}
