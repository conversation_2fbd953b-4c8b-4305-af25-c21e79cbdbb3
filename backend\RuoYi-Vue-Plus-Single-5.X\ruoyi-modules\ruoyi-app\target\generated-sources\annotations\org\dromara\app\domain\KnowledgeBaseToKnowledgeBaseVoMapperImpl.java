package org.dromara.app.domain;

import java.time.LocalDateTime;
import java.time.ZoneId;
import javax.annotation.processing.Generated;
import org.dromara.app.domain.vo.KnowledgeBaseVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T12:02:22+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.10 (Oracle Corporation)"
)
@Component
public class KnowledgeBaseToKnowledgeBaseVoMapperImpl implements KnowledgeBaseToKnowledgeBaseVoMapper {

    @Override
    public KnowledgeBaseVo convert(KnowledgeBase arg0) {
        if ( arg0 == null ) {
            return null;
        }

        KnowledgeBaseVo knowledgeBaseVo = new KnowledgeBaseVo();

        knowledgeBaseVo.setId( arg0.getId() );
        knowledgeBaseVo.setName( arg0.getName() );
        knowledgeBaseVo.setDescription( arg0.getDescription() );
        knowledgeBaseVo.setType( arg0.getType() );
        knowledgeBaseVo.setStatus( arg0.getStatus() );
        knowledgeBaseVo.setVectorDimension( arg0.getVectorDimension() );
        knowledgeBaseVo.setDocumentCount( arg0.getDocumentCount() );
        knowledgeBaseVo.setVectorCount( arg0.getVectorCount() );
        knowledgeBaseVo.setIndexConfig( arg0.getIndexConfig() );
        knowledgeBaseVo.setExtendConfig( arg0.getExtendConfig() );
        knowledgeBaseVo.setLastSyncTime( arg0.getLastSyncTime() );
        knowledgeBaseVo.setSortOrder( arg0.getSortOrder() );
        knowledgeBaseVo.setRemark( arg0.getRemark() );
        knowledgeBaseVo.setCreateDept( arg0.getCreateDept() );
        knowledgeBaseVo.setCreateBy( arg0.getCreateBy() );
        if ( arg0.getCreateTime() != null ) {
            knowledgeBaseVo.setCreateTime( LocalDateTime.ofInstant( arg0.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        knowledgeBaseVo.setUpdateBy( arg0.getUpdateBy() );
        if ( arg0.getUpdateTime() != null ) {
            knowledgeBaseVo.setUpdateTime( LocalDateTime.ofInstant( arg0.getUpdateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }

        return knowledgeBaseVo;
    }

    @Override
    public KnowledgeBaseVo convert(KnowledgeBase arg0, KnowledgeBaseVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setDescription( arg0.getDescription() );
        arg1.setType( arg0.getType() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setVectorDimension( arg0.getVectorDimension() );
        arg1.setDocumentCount( arg0.getDocumentCount() );
        arg1.setVectorCount( arg0.getVectorCount() );
        arg1.setIndexConfig( arg0.getIndexConfig() );
        arg1.setExtendConfig( arg0.getExtendConfig() );
        arg1.setLastSyncTime( arg0.getLastSyncTime() );
        arg1.setSortOrder( arg0.getSortOrder() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        if ( arg0.getCreateTime() != null ) {
            arg1.setCreateTime( LocalDateTime.ofInstant( arg0.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        else {
            arg1.setCreateTime( null );
        }
        arg1.setUpdateBy( arg0.getUpdateBy() );
        if ( arg0.getUpdateTime() != null ) {
            arg1.setUpdateTime( LocalDateTime.ofInstant( arg0.getUpdateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        else {
            arg1.setUpdateTime( null );
        }

        return arg1;
    }
}
