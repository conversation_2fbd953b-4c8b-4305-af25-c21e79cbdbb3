package org.dromara.app.domain.vo;

import java.time.ZoneOffset;
import java.util.Date;
import javax.annotation.processing.Generated;
import org.dromara.app.domain.KnowledgeBase;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T12:02:22+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.10 (Oracle Corporation)"
)
@Component
public class KnowledgeBaseVoToKnowledgeBaseMapperImpl implements KnowledgeBaseVoToKnowledgeBaseMapper {

    @Override
    public KnowledgeBase convert(KnowledgeBaseVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        KnowledgeBase knowledgeBase = new KnowledgeBase();

        knowledgeBase.setCreateDept( arg0.getCreateDept() );
        knowledgeBase.setCreateBy( arg0.getCreateBy() );
        if ( arg0.getCreateTime() != null ) {
            knowledgeBase.setCreateTime( Date.from( arg0.getCreateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        knowledgeBase.setUpdateBy( arg0.getUpdateBy() );
        if ( arg0.getUpdateTime() != null ) {
            knowledgeBase.setUpdateTime( Date.from( arg0.getUpdateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        knowledgeBase.setId( arg0.getId() );
        knowledgeBase.setName( arg0.getName() );
        knowledgeBase.setDescription( arg0.getDescription() );
        knowledgeBase.setType( arg0.getType() );
        knowledgeBase.setStatus( arg0.getStatus() );
        knowledgeBase.setVectorDimension( arg0.getVectorDimension() );
        knowledgeBase.setDocumentCount( arg0.getDocumentCount() );
        knowledgeBase.setVectorCount( arg0.getVectorCount() );
        knowledgeBase.setIndexConfig( arg0.getIndexConfig() );
        knowledgeBase.setExtendConfig( arg0.getExtendConfig() );
        knowledgeBase.setLastSyncTime( arg0.getLastSyncTime() );
        knowledgeBase.setSortOrder( arg0.getSortOrder() );
        knowledgeBase.setRemark( arg0.getRemark() );

        return knowledgeBase;
    }

    @Override
    public KnowledgeBase convert(KnowledgeBaseVo arg0, KnowledgeBase arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        if ( arg0.getCreateTime() != null ) {
            arg1.setCreateTime( Date.from( arg0.getCreateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        else {
            arg1.setCreateTime( null );
        }
        arg1.setUpdateBy( arg0.getUpdateBy() );
        if ( arg0.getUpdateTime() != null ) {
            arg1.setUpdateTime( Date.from( arg0.getUpdateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        else {
            arg1.setUpdateTime( null );
        }
        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setDescription( arg0.getDescription() );
        arg1.setType( arg0.getType() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setVectorDimension( arg0.getVectorDimension() );
        arg1.setDocumentCount( arg0.getDocumentCount() );
        arg1.setVectorCount( arg0.getVectorCount() );
        arg1.setIndexConfig( arg0.getIndexConfig() );
        arg1.setExtendConfig( arg0.getExtendConfig() );
        arg1.setLastSyncTime( arg0.getLastSyncTime() );
        arg1.setSortOrder( arg0.getSortOrder() );
        arg1.setRemark( arg0.getRemark() );

        return arg1;
    }
}
