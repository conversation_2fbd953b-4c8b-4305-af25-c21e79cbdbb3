package org.dromara.app.domain;

import javax.annotation.processing.Generated;
import org.dromara.app.domain.vo.AppUserManageVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T12:02:22+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.10 (Oracle Corporation)"
)
@Component
public class AppUserToAppUserManageVoMapperImpl implements AppUserToAppUserManageVoMapper {

    @Override
    public AppUserManageVo convert(AppUser arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppUserManageVo appUserManageVo = new AppUserManageVo();

        appUserManageVo.setUserId( arg0.getUserId() );
        appUserManageVo.setPhone( arg0.getPhone() );
        appUserManageVo.setEmail( arg0.getEmail() );
        appUserManageVo.setRealName( arg0.getRealName() );
        appUserManageVo.setGender( arg0.getGender() );
        appUserManageVo.setStudentId( arg0.getStudentId() );
        appUserManageVo.setMajor( arg0.getMajor() );
        appUserManageVo.setGrade( arg0.getGrade() );
        appUserManageVo.setSchool( arg0.getSchool() );
        appUserManageVo.setIntroduction( arg0.getIntroduction() );
        appUserManageVo.setAvatar( arg0.getAvatar() );
        appUserManageVo.setStatus( arg0.getStatus() );
        appUserManageVo.setLoginIp( arg0.getLoginIp() );
        appUserManageVo.setLoginDate( arg0.getLoginDate() );
        appUserManageVo.setRegisteredAt( arg0.getRegisteredAt() );
        appUserManageVo.setCreateTime( arg0.getCreateTime() );
        appUserManageVo.setUpdateTime( arg0.getUpdateTime() );
        appUserManageVo.setRemark( arg0.getRemark() );

        return appUserManageVo;
    }

    @Override
    public AppUserManageVo convert(AppUser arg0, AppUserManageVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUserId( arg0.getUserId() );
        arg1.setPhone( arg0.getPhone() );
        arg1.setEmail( arg0.getEmail() );
        arg1.setRealName( arg0.getRealName() );
        arg1.setGender( arg0.getGender() );
        arg1.setStudentId( arg0.getStudentId() );
        arg1.setMajor( arg0.getMajor() );
        arg1.setGrade( arg0.getGrade() );
        arg1.setSchool( arg0.getSchool() );
        arg1.setIntroduction( arg0.getIntroduction() );
        arg1.setAvatar( arg0.getAvatar() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setLoginIp( arg0.getLoginIp() );
        arg1.setLoginDate( arg0.getLoginDate() );
        arg1.setRegisteredAt( arg0.getRegisteredAt() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setRemark( arg0.getRemark() );

        return arg1;
    }
}
